<script lang="ts">
	import MessageInput from '$lib/components/conversation/MessageInput.svelte';
	import { t } from '$lib/stores/i18n';

	let messageContent = '';
	let messageType = 'TEXT';
	let files: any[] = [];

	function handleSendMessage(event: CustomEvent) {
		const { content, type, preUploadedFiles } = event.detail;
		console.log('Message sent:', { content, type, preUploadedFiles });
		
		// Display the sent message
		messageContent = content;
		messageType = type;
		files = preUploadedFiles || [];
		
		alert(`Message sent!\nContent: ${content}\nType: ${type}\nFiles: ${preUploadedFiles?.length || 0}`);
	}

	function handleTyping(event: CustomEvent) {
		const { isTyping } = event.detail;
		console.log('Typing status:', isTyping);
	}
</script>

<div class="container mx-auto p-8">
	<h1 class="text-3xl font-bold mb-8">MessageInput Component Test</h1>
	
	<div class="bg-white rounded-lg shadow-lg p-6 mb-8">
		<h2 class="text-xl font-semibold mb-4">Test Instructions</h2>
		<div class="space-y-2 text-sm text-gray-600">
			<p>1. <strong>Toggle Button Test:</strong> Click the edit icon (✏️) in the top-right corner of the input area to switch between plain text and rich text modes.</p>
			<p>2. <strong>Rich Text Input Test:</strong> When in rich text mode, try typing text and using the formatting toolbar (Bold, Italic, etc.).</p>
			<p>3. <strong>Plain Text Input Test:</strong> When in plain text mode, try typing normal text.</p>
			<p>4. <strong>Content Persistence:</strong> Switch between modes and verify that content is preserved and converted properly.</p>
		</div>
	</div>

	<div class="bg-gray-50 rounded-lg p-6 mb-8">
		<h2 class="text-xl font-semibold mb-4">MessageInput Component</h2>
		<div class="border border-gray-200 rounded-lg">
			<MessageInput
				disabled={false}
				canSendMessage={true}
				isNotTicketOwner={false}
				isTicketPendingToClose={false}
				isTicketClosed={false}
				conversationId="test-conversation"
				customerId={1}
				platformId={1}
				on:send={handleSendMessage}
				on:typing={handleTyping}
			/>
		</div>
	</div>

	{#if messageContent}
		<div class="bg-green-50 border border-green-200 rounded-lg p-6">
			<h2 class="text-xl font-semibold mb-4 text-green-800">Last Sent Message</h2>
			<div class="space-y-2">
				<p><strong>Content:</strong> {messageContent}</p>
				<p><strong>Type:</strong> {messageType}</p>
				<p><strong>Files:</strong> {files.length}</p>
			</div>
		</div>
	{/if}
</div>
