<script lang="ts">
	import { onMount, onDestroy, createEventDispatcher } from 'svelte';
	import { Editor, EditorContent } from 'svelte-tiptap';
	import StarterKit from '@tiptap/starter-kit';
	import { TextStyle } from '@tiptap/extension-text-style';
	import { Color } from '@tiptap/extension-color';
	import { TextAlign } from '@tiptap/extension-text-align';
	import { FontFamily } from '@tiptap/extension-font-family';
	import { Link } from '@tiptap/extension-link';
	import { CharacterCount } from '@tiptap/extension-character-count';

	const dispatch = createEventDispatcher();

	export let content = '';
	export let placeholder = 'Type your message...';
	export let maxLength = 200;
	export let disabled = false;
	export let readonly = false;
	export let minHeight = '40px';
	export let maxHeight = '120px';
	export let enableEnterToSend = true;

	let editor: Editor;
	let showColorPicker = false;
	let showFontSizePicker = false;
	let colorPickerButton: HTMLElement;
	let fontSizeButton: HTMLElement;

	// Color options
	const colors = [
		'#000000', '#374151', '#6B7280', '#9CA3AF',
		'#EF4444', '#F97316', '#EAB308', '#22C55E',
		'#3B82F6', '#8B5CF6', '#EC4899', '#F43F5E'
	];

	// Font size options
	const fontSizes = [
		{ label: 'Small', value: '12px' },
		{ label: 'Normal', value: '14px' },
		{ label: 'Large', value: '18px' }
	];

	onMount(() => {
		editor = new Editor({
			// Remove element binding - let EditorContent handle DOM mounting
			extensions: [
				StarterKit.configure({
					heading: false, // Disable headings for simple formatting
					codeBlock: false, // Disable code blocks
					blockquote: false, // Disable blockquotes
				}),
				TextStyle,
				Color,
				TextAlign.configure({
					types: ['paragraph'],
				}),
				FontFamily,
				Link.configure({
					openOnClick: false,
					HTMLAttributes: {
						class: 'text-blue-500 underline',
					},
				}),
				CharacterCount.configure({
					limit: maxLength,
				}),
			],
			content: content,
			editorProps: {
				attributes: {
					class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
					style: `min-height: ${minHeight}; max-height: ${maxHeight}; overflow-y: auto;`,
				},
				handleKeyDown: (_view, event) => {
					// Handle Enter key for sending
					if (enableEnterToSend && event.key === 'Enter' && !event.shiftKey) {
						event.preventDefault();
						dispatch('send');
						return true;
					}
					return false;
				},
			},
			onTransaction: () => {
				// Force re-render
				editor = editor;
			},
			onUpdate: ({ editor }) => {
				const html = editor.getHTML();
				const text = editor.getText();
				dispatch('update', { html, text });
			},
			onFocus: () => {
				dispatch('focus');
			},
			onBlur: () => {
				dispatch('blur');
			},
		});

		// Handle clicks outside color/font pickers
		document.addEventListener('click', handleClickOutside);
	});

	onDestroy(() => {
		if (editor) {
			editor.destroy();
		}
		document.removeEventListener('click', handleClickOutside);
	});

	function handleClickOutside(event: MouseEvent) {
		if (showColorPicker && colorPickerButton && !colorPickerButton.contains(event.target as Node)) {
			showColorPicker = false;
		}
		if (showFontSizePicker && fontSizeButton && !fontSizeButton.contains(event.target as Node)) {
			showFontSizePicker = false;
		}
	}

	function toggleBold() {
		editor.chain().focus().toggleBold().run();
	}

	function toggleItalic() {
		editor.chain().focus().toggleItalic().run();
	}

	function toggleUnderline() {
		editor.chain().focus().toggleUnderline().run();
	}

	function toggleStrike() {
		editor.chain().focus().toggleStrike().run();
	}

	function toggleBulletList() {
		editor.chain().focus().toggleBulletList().run();
	}

	function toggleOrderedList() {
		editor.chain().focus().toggleOrderedList().run();
	}

	function setTextAlign(alignment: string) {
		editor.chain().focus().setTextAlign(alignment).run();
	}

	function setColor(color: string) {
		editor.chain().focus().setColor(color).run();
		showColorPicker = false;
	}

	function setFontSize(size: string) {
		editor.chain().focus().setFontSize(size).run();
		showFontSizePicker = false;
	}

	function addLink() {
		const url = window.prompt('Enter URL:');
		if (url) {
			editor.chain().focus().setLink({ href: url }).run();
		}
	}

	function removeLink() {
		editor.chain().focus().unsetLink().run();
	}

	// Public methods
	export function getHTML() {
		return editor?.getHTML() || '';
	}

	export function getText() {
		return editor?.getText() || '';
	}

	export function setContent(newContent: string) {
		if (editor) {
			editor.commands.setContent(newContent);
		}
	}

	export function focus() {
		if (editor) {
			editor.commands.focus();
		}
	}

	export function blur() {
		if (editor) {
			editor.commands.blur();
		}
	}

	// Reactive statements - only update content if it's significantly different
	// to avoid interfering with user input
	$: if (editor && content !== editor.getHTML()) {
		// Only update if the editor is not focused (to avoid interrupting user input)
		// or if the content is empty (for initialization)
		if (!editor.isFocused || content === '' || content === '<p></p>') {
			editor.commands.setContent(content);
		}
	}

	$: if (editor) {
		editor.setEditable(!disabled && !readonly);
	}

	$: characterCount = editor?.storage.characterCount.characters() || 0;
	$: isAtLimit = characterCount >= maxLength;
</script>

<div class="rich-text-editor border border-gray-300 rounded-lg focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-opacity-20">
	<!-- Toolbar -->
	<div class="flex flex-wrap items-center gap-1 p-2 border-b border-gray-200 bg-gray-50">
		<!-- Text formatting -->
		<div class="flex items-center gap-1">
			<button
				type="button"
				on:click={toggleBold}
				class="px-2 py-1 text-xs font-bold rounded hover:bg-gray-200 {editor?.isActive('bold') ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}"
				title="Bold"
				disabled={disabled || readonly}
			>
				B
			</button>

			<button
				type="button"
				on:click={toggleItalic}
				class="px-2 py-1 text-xs italic rounded hover:bg-gray-200 {editor?.isActive('italic') ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}"
				title="Italic"
				disabled={disabled || readonly}
			>
				I
			</button>

			<button
				type="button"
				on:click={toggleUnderline}
				class="px-2 py-1 text-xs underline rounded hover:bg-gray-200 {editor?.isActive('underline') ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}"
				title="Underline"
				disabled={disabled || readonly}
			>
				U
			</button>

			<button
				type="button"
				on:click={toggleStrike}
				class="px-2 py-1 text-xs line-through rounded hover:bg-gray-200 {editor?.isActive('strike') ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}"
				title="Strikethrough"
				disabled={disabled || readonly}
			>
				S
			</button>
		</div>

		<div class="w-px h-6 bg-gray-300"></div>

		<!-- Lists -->
		<div class="flex items-center gap-1">
			<button
				type="button"
				on:click={toggleBulletList}
				class="px-2 py-1 text-xs rounded hover:bg-gray-200 {editor?.isActive('bulletList') ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}"
				title="Bullet List"
				disabled={disabled || readonly}
			>
				•
			</button>

			<button
				type="button"
				on:click={toggleOrderedList}
				class="px-2 py-1 text-xs rounded hover:bg-gray-200 {editor?.isActive('orderedList') ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}"
				title="Numbered List"
				disabled={disabled || readonly}
			>
				1.
			</button>
		</div>

		<div class="w-px h-6 bg-gray-300"></div>

		<!-- Alignment -->
		<div class="flex items-center gap-1">
			<button
				type="button"
				on:click={() => setTextAlign('left')}
				class="px-2 py-1 text-xs rounded hover:bg-gray-200 {editor?.isActive({ textAlign: 'left' }) ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}"
				title="Align Left"
				disabled={disabled || readonly}
			>
				⬅
			</button>

			<button
				type="button"
				on:click={() => setTextAlign('center')}
				class="px-2 py-1 text-xs rounded hover:bg-gray-200 {editor?.isActive({ textAlign: 'center' }) ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}"
				title="Align Center"
				disabled={disabled || readonly}
			>
				↔
			</button>

			<button
				type="button"
				on:click={() => setTextAlign('right')}
				class="px-2 py-1 text-xs rounded hover:bg-gray-200 {editor?.isActive({ textAlign: 'right' }) ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}"
				title="Align Right"
				disabled={disabled || readonly}
			>
				➡
			</button>
		</div>

		<div class="w-px h-6 bg-gray-300"></div>

		<!-- Color picker -->
		<div class="relative">
			<button
				bind:this={colorPickerButton}
				type="button"
				on:click={() => showColorPicker = !showColorPicker}
				class="px-2 py-1 text-xs rounded hover:bg-gray-200 text-gray-600"
				title="Text Color"
				disabled={disabled || readonly}
			>
				🎨
			</button>
			
			{#if showColorPicker}
				<div class="absolute top-full left-0 mt-1 p-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
					<div class="grid grid-cols-4 gap-1">
						{#each colors as color}
							<button
								type="button"
								on:click={() => setColor(color)}
								class="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
								style="background-color: {color}"
								title={color}
							></button>
						{/each}
					</div>
				</div>
			{/if}
		</div>

		<!-- Font size picker -->
		<div class="relative">
			<button
				bind:this={fontSizeButton}
				type="button"
				on:click={() => showFontSizePicker = !showFontSizePicker}
				class="px-2 py-1 text-xs rounded hover:bg-gray-200 text-gray-600"
				title="Font Size"
				disabled={disabled || readonly}
			>
				A
			</button>
			
			{#if showFontSizePicker}
				<div class="absolute top-full left-0 mt-1 p-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-20">
					{#each fontSizes as fontSize}
						<button
							type="button"
							on:click={() => setFontSize(fontSize.value)}
							class="block w-full px-3 py-1 text-left text-sm hover:bg-gray-100 rounded"
						>
							{fontSize.label}
						</button>
					{/each}
				</div>
			{/if}
		</div>

		<!-- Link -->
		<div class="flex items-center gap-1">
			<button
				type="button"
				on:click={addLink}
				class="px-2 py-1 text-xs rounded hover:bg-gray-200 {editor?.isActive('link') ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}"
				title="Add Link"
				disabled={disabled || readonly}
			>
				🔗
			</button>

			{#if editor?.isActive('link')}
				<button
					type="button"
					on:click={removeLink}
					class="px-2 py-1 text-xs rounded hover:bg-gray-200 text-red-600"
					title="Remove Link"
					disabled={disabled || readonly}
				>
					×
				</button>
			{/if}
		</div>
	</div>

	<!-- Editor content -->
	<div class="relative">
		<div
			class="px-3 py-2 text-sm focus:outline-none {disabled || readonly ? 'cursor-not-allowed bg-gray-100' : ''}"
			style="min-height: {minHeight}; max-height: {maxHeight};"
		>
			{#if editor}
				<EditorContent {editor} />
			{/if}

			<!-- Placeholder -->
			{#if characterCount === 0 && placeholder}
				<div class="absolute top-2 left-3 text-gray-400 text-sm pointer-events-none">
					{placeholder}
				</div>
			{/if}
		</div>

		<!-- Character count -->
		<div class="absolute bottom-1 right-2 text-xs {isAtLimit ? 'text-red-500' : 'text-gray-400'}">
			{characterCount}/{maxLength}
		</div>
	</div>
</div>

<style>
	:global(.ProseMirror) {
		outline: none !important;
		padding: 0;
		margin: 0;
		min-height: inherit;
		max-height: inherit;
		overflow-y: auto;
	}

	:global(.ProseMirror p) {
		margin: 0;
		padding: 0;
	}

	:global(.ProseMirror ul, .ProseMirror ol) {
		margin: 0.5em 0;
		padding-left: 1.5em;
	}

	:global(.ProseMirror li) {
		margin: 0.25em 0;
	}

	:global(.ProseMirror a) {
		color: #3b82f6;
		text-decoration: underline;
	}

	:global(.ProseMirror strong) {
		font-weight: bold;
	}

	:global(.ProseMirror em) {
		font-style: italic;
	}

	:global(.ProseMirror u) {
		text-decoration: underline;
	}

	:global(.ProseMirror s) {
		text-decoration: line-through;
	}
</style>
