import { t } from "../stores/i18n";
import { processRichTextContent, htmlToPlainText, isHtmlContent } from './richTextUtils';

/**
 * Result type for message validation and sanitization
 */
export interface MessageValidationResult {
    sanitizedContent: string;
    isValid: boolean;
    error: string;
    isHtml?: boolean;
    plainText?: string;
}

/**
 * Unified function that validates and sanitizes message input for submission
 * Performs comprehensive security checks, content sanitization, and validation
 * Supports both plain text and rich text (HTML) content
 */
export function validateAndSanitizeMessage(input: string, isRichText: boolean = false): MessageValidationResult {
    // Early validation check for empty input
    if (!input || input.trim() === '') {
        return {
            sanitizedContent: '',
            isValid: true,
            error: '',
            isHtml: isRichText,
            plainText: ''
        };
    }

    // Handle rich text content
    if (isRichText || isHtmlContent(input)) {
        return validateAndSanitizeRichText(input);
    }

    // Handle plain text content (existing logic)
    let sanitized = input;

    // Remove script tags and their content (preserve whitespace outside of tags)
    sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');

    // Remove dangerous HTML tags while preserving surrounding whitespace
    const dangerousTags = [
        'script', 'iframe', 'object', 'embed', 'form', 'input', 'button',
        'link', 'meta', 'style', 'base', 'applet', 'frame', 'frameset',
        'div', 'span', 'img', 'a', 'p', 'br', 'hr', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'ul', 'ol', 'li', 'table', 'thead', 'tbody', 'tfoot', 'tr', 'th', 'td', 'pre',
        'code', 'blockquote', 'address', 'ins', 'del', 's', 'u', 'b', 'i', 'em', 'strong',
        'mark', 'small', 'big', 'sup', 'sub', 'tt', 'var', 'cite', 'dfn', 'abbr', 'acronym',
        'bdo', 'q', 'samp', 'kbd', 'param', 'map', 'area', 'noframes',
        'select', 'option', 'optgroup', 'textarea', 'label', 'fieldset',
        'legend', 'datalist', 'keygen', 'output', 'progress', 'meter', 'details', 'summary',
        'command', 'menu', 'canvas', 'audio', 'video', 'source', 'track', 'noscript'
    ];

    dangerousTags.forEach(tag => {
        const regex = new RegExp(`<\\/?${tag}\\b[^>]*>`, 'gi');
        sanitized = sanitized.replace(regex, '');
    });

    // Remove javascript:, data: protocols, and alert() calls
    sanitized = sanitized.replace(/javascript\s*:/gi, '');
    sanitized = sanitized.replace(/data\s*:/gi, '');
    sanitized = sanitized.replace(/alert\s*\([^)]*\)/gi, '');

    // Remove on* event handlers with more precise matching
    // This targets actual HTML attributes, not normal text containing "on"
    sanitized = sanitized.replace(/\s+on\w+\s*=\s*["'][^"']*["']/gi, '');
    sanitized = sanitized.replace(/\s+on\w+\s*=\s*[^\s>]+/gi, '');

    // Only limit consecutive angle brackets that could be malicious HTML
    // Preserve normal greater/less than symbols in text
    sanitized = sanitized.replace(/[<>]{3,}/g, '<<>>');
    sanitized = sanitized.replace(/[{}]{3,}/g, '{{}}');

    // Remove any remaining HTML-like patterns that could be dangerous
    // But be more selective to avoid removing legitimate content
    sanitized = sanitized.replace(/<\s*\/?\s*[a-zA-Z][^>]*>/gi, '');

    // Only trim leading/trailing whitespace, preserve internal whitespace
    sanitized = sanitized.replace(/^\s+|\s+$/g, '');

    // Final validation check for any remaining suspicious patterns after sanitization
    const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /vbscript:/i,
        /onload=/i,
        /onerror=/i,
        /onclick=/i
    ];

    for (const pattern of suspiciousPatterns) {
        if (pattern.test(sanitized) || sanitized.trim() === '') {
            return {
                sanitizedContent: sanitized,
                isValid: false,
                error: t('input_error_message_contains_invalid_content')
            };
        }
    }

    return {
        sanitizedContent: sanitized,
        isValid: true,
        error: '',
        isHtml: false,
        plainText: sanitized
    };
}

/**
 * Validate and sanitize rich text (HTML) content
 */
function validateAndSanitizeRichText(input: string): MessageValidationResult {
    try {
        // Process the rich text content
        const processed = processRichTextContent(input);

        // Check if content is empty
        if (processed.isEmpty) {
            return {
                sanitizedContent: '',
                isValid: true,
                error: '',
                isHtml: true,
                plainText: ''
            };
        }

        // Validate character count using plain text
        if (processed.characterCount > 200) {
            return {
                sanitizedContent: processed.html,
                isValid: false,
                error: t('input_error_message_too_long'),
                isHtml: true,
                plainText: processed.plainText
            };
        }

        // Additional security validation on the plain text version
        const suspiciousPatterns = [
            /javascript:/i,
            /vbscript:/i,
            /data:/i,
            /onload=/i,
            /onerror=/i,
            /onclick=/i,
            /<script/i
        ];

        for (const pattern of suspiciousPatterns) {
            if (pattern.test(processed.html) || pattern.test(processed.plainText)) {
                return {
                    sanitizedContent: processed.html,
                    isValid: false,
                    error: t('input_error_message_contains_invalid_content'),
                    isHtml: true,
                    plainText: processed.plainText
                };
            }
        }

        return {
            sanitizedContent: processed.html,
            isValid: true,
            error: '',
            isHtml: true,
            plainText: processed.plainText
        };
    } catch (error) {
        // If rich text processing fails, fall back to plain text
        console.warn('Rich text processing failed, falling back to plain text:', error);
        const plainText = htmlToPlainText(input);
        return validateAndSanitizeMessage(plainText, false);
    }
}
