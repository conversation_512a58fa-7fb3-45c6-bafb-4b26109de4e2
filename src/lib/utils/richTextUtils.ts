/**
 * Utility functions for rich text editor content conversion and sanitization
 */

/**
 * Convert HTML content to plain text
 * Preserves basic formatting like line breaks and list structure
 */
export function htmlToPlainText(html: string): string {
	if (!html || html.trim() === '') {
		return '';
	}

	// Create a temporary DOM element to parse HTML
	const tempDiv = document.createElement('div');
	tempDiv.innerHTML = html;

	// Convert lists to plain text with proper formatting
	const listItems = tempDiv.querySelectorAll('li');
	listItems.forEach((li, index) => {
		const parent = li.parentElement;
		const isOrdered = parent?.tagName.toLowerCase() === 'ol';
		const prefix = isOrdered ? `${index + 1}. ` : '• ';
		li.textContent = prefix + (li.textContent || '');
	});

	// Convert paragraphs to have line breaks
	const paragraphs = tempDiv.querySelectorAll('p');
	paragraphs.forEach((p, index) => {
		if (index > 0) {
			p.textContent = '\n' + (p.textContent || '');
		}
	});

	// Get the plain text content
	let plainText = tempDiv.textContent || tempDiv.innerText || '';

	// Clean up extra whitespace
	plainText = plainText.replace(/\n\s*\n/g, '\n').trim();

	return plainText;
}

/**
 * Convert plain text to basic HTML
 * Preserves line breaks and basic formatting
 */
export function plainTextToHtml(text: string): string {
	if (!text || text.trim() === '') {
		return '<p></p>';
	}

	// Split by double line breaks for paragraphs
	const paragraphs = text.split(/\n\s*\n/);
	
	const htmlParagraphs = paragraphs.map(paragraph => {
		// Handle single line breaks within paragraphs
		const lines = paragraph.split('\n').map(line => line.trim()).filter(line => line);
		
		if (lines.length === 0) {
			return '<p></p>';
		}
		
		// Check if this looks like a list
		const listItems = lines.filter(line => 
			line.match(/^(\d+\.\s|•\s|-\s|\*\s)/) || 
			line.match(/^\s*[\d]+\.\s/) ||
			line.match(/^\s*[•\-\*]\s/)
		);
		
		if (listItems.length > 0 && listItems.length === lines.length) {
			// This is a list
			const isOrdered = lines[0].match(/^\s*\d+\.\s/);
			const listTag = isOrdered ? 'ol' : 'ul';
			
			const listItemsHtml = lines.map(line => {
				// Remove list markers
				const content = line.replace(/^(\s*(\d+\.\s|[•\-\*]\s))/, '').trim();
				return `<li>${escapeHtml(content)}</li>`;
			}).join('');
			
			return `<${listTag}>${listItemsHtml}</${listTag}>`;
		} else {
			// Regular paragraph
			const content = lines.join('<br>');
			return `<p>${escapeHtml(content)}</p>`;
		}
	});

	return htmlParagraphs.join('');
}

/**
 * Escape HTML characters to prevent XSS
 */
function escapeHtml(text: string): string {
	const div = document.createElement('div');
	div.textContent = text;
	return div.innerHTML;
}

/**
 * Check if content is HTML (contains HTML tags)
 */
export function isHtmlContent(content: string): boolean {
	if (!content) return false;
	
	// Check for common HTML tags
	const htmlTagRegex = /<\/?[a-z][\s\S]*>/i;
	return htmlTagRegex.test(content);
}

/**
 * Get character count from HTML content (excluding HTML tags)
 */
export function getHtmlCharacterCount(html: string): number {
	if (!html) return 0;
	
	const tempDiv = document.createElement('div');
	tempDiv.innerHTML = html;
	const text = tempDiv.textContent || tempDiv.innerText || '';
	return text.length;
}

/**
 * Truncate HTML content to a specific character limit
 * Preserves HTML structure as much as possible
 */
export function truncateHtml(html: string, maxLength: number): string {
	if (!html) return '';
	
	const tempDiv = document.createElement('div');
	tempDiv.innerHTML = html;
	
	const text = tempDiv.textContent || tempDiv.innerText || '';
	
	if (text.length <= maxLength) {
		return html;
	}
	
	// If we need to truncate, convert to plain text first, truncate, then convert back
	const plainText = htmlToPlainText(html);
	const truncatedText = plainText.substring(0, maxLength);
	
	return plainTextToHtml(truncatedText);
}

/**
 * Sanitize HTML content for rich text editor
 * Allows only safe formatting tags
 */
export function sanitizeRichTextHtml(html: string): string {
	if (!html) return '';

	// Create a temporary DOM element
	const tempDiv = document.createElement('div');
	tempDiv.innerHTML = html;

	// Define allowed tags and their allowed attributes
	const allowedTags = {
		'p': [],
		'br': [],
		'strong': [],
		'b': [],
		'em': [],
		'i': [],
		'u': [],
		's': [],
		'ul': [],
		'ol': [],
		'li': [],
		'a': ['href', 'title'],
		'span': ['style'] // For colors and font sizes
	};

	// Function to clean an element
	function cleanElement(element: Element): Element | null {
		const tagName = element.tagName.toLowerCase();
		
		// If tag is not allowed, return its text content wrapped in a span
		if (!allowedTags.hasOwnProperty(tagName)) {
			const span = document.createElement('span');
			span.textContent = element.textContent || '';
			return span;
		}

		// Create a new clean element
		const cleanEl = document.createElement(tagName);
		
		// Copy allowed attributes
		const allowedAttrs = allowedTags[tagName as keyof typeof allowedTags];
		for (const attr of element.attributes) {
			if (allowedAttrs.includes(attr.name)) {
				// Additional validation for specific attributes
				if (attr.name === 'href') {
					// Only allow http, https, and mailto links
					const href = attr.value;
					if (href.match(/^(https?:\/\/|mailto:)/i)) {
						cleanEl.setAttribute(attr.name, attr.value);
					}
				} else if (attr.name === 'style') {
					// Only allow safe CSS properties
					const style = attr.value;
					const safeStyles = style.split(';').filter(s => {
						const prop = s.split(':')[0]?.trim().toLowerCase();
						return ['color', 'font-size', 'text-align'].includes(prop);
					}).join(';');
					if (safeStyles) {
						cleanEl.setAttribute('style', safeStyles);
					}
				} else {
					cleanEl.setAttribute(attr.name, attr.value);
				}
			}
		}

		// Recursively clean child elements
		for (const child of Array.from(element.childNodes)) {
			if (child.nodeType === Node.TEXT_NODE) {
				cleanEl.appendChild(document.createTextNode(child.textContent || ''));
			} else if (child.nodeType === Node.ELEMENT_NODE) {
				const cleanChild = cleanElement(child as Element);
				if (cleanChild) {
					cleanEl.appendChild(cleanChild);
				}
			}
		}

		return cleanEl;
	}

	// Clean all child elements
	const cleanDiv = document.createElement('div');
	for (const child of Array.from(tempDiv.childNodes)) {
		if (child.nodeType === Node.TEXT_NODE) {
			cleanDiv.appendChild(document.createTextNode(child.textContent || ''));
		} else if (child.nodeType === Node.ELEMENT_NODE) {
			const cleanChild = cleanElement(child as Element);
			if (cleanChild) {
				cleanDiv.appendChild(cleanChild);
			}
		}
	}

	return cleanDiv.innerHTML;
}

/**
 * Check if HTML content is empty (contains only empty tags)
 */
export function isEmptyHtml(html: string): boolean {
	if (!html) return true;
	
	const tempDiv = document.createElement('div');
	tempDiv.innerHTML = html;
	const text = (tempDiv.textContent || tempDiv.innerText || '').trim();
	
	return text === '';
}

/**
 * Convert rich text editor content for storage/transmission
 * Returns both HTML and plain text versions
 */
export function processRichTextContent(html: string): {
	html: string;
	plainText: string;
	isEmpty: boolean;
	characterCount: number;
} {
	const sanitizedHtml = sanitizeRichTextHtml(html);
	const plainText = htmlToPlainText(sanitizedHtml);
	const isEmpty = isEmptyHtml(sanitizedHtml);
	const characterCount = getHtmlCharacterCount(sanitizedHtml);

	return {
		html: sanitizedHtml,
		plainText,
		isEmpty,
		characterCount
	};
}
